import "./editor.css";

import React, { useRef, useEffect, useCallback } from "react";
import { Editor } from "@tinymce/tinymce-react";
import InputHelper from "../CustomFormHelperText/InputHelper";

interface TinyMCEEditorProps {
  initialValue?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  isRTL?: boolean;
  error?: boolean;
  helperText?: React.ReactNode;
}

function isHTMLString(str?: string) {
  if (!str) return false;
  const htmlTagPattern = /<[^>]+>/;
  return htmlTagPattern.test(str);
}

const apiKey = "9nkuceeijwa1o5zd7mec4t2oqyk1usbxn1ddoftxhpy6h2hk";

export default function TinyMCEEditor({
  autoFocus = false,
  initialValue = "",
  onChange,
  placeholder = "",
  error = false,
  helperText,
  isRTL = true
}: TinyMCEEditorProps) {
  const editorRef = useRef<any>(null);

  // Process initial value to ensure proper format
  const processedInitialValue = React.useMemo(() => {
    if (!initialValue) return "<p><br></p>";

    if (isHTMLString(initialValue)) {
      // Clean up HTML and ensure proper structure
      let processedHtml = initialValue.replace(/<p><\/p>/g, "<p><br></p>").trim();
      return processedHtml || "<p><br></p>";
    } else {
      // Convert plain text to HTML, preserving line breaks
      const lines = initialValue.split(/\r?\n/);
      if (lines.length === 1) {
        return `<p>${lines[0] || "<br>"}</p>`;
      } else {
        return lines.map(line => `<p>${line || "<br>"}</p>`).join("");
      }
    }
  }, [initialValue]);

  // Handle content change
  const handleEditorChange = useCallback(
    (content: string) => {
      if (onChange) {
        // Clean up the HTML output
        let processedHtml = content.replace(/<p><\/p>/g, "<p><br></p>").trim();

        // If empty, provide default empty paragraph
        if (!processedHtml || processedHtml === "<p></p>") {
          processedHtml = "<p><br></p>";
        }

        onChange(processedHtml);
      }
    },
    [onChange]
  );

  // TinyMCE configuration - FREE self-hosted version
  const editorConfig = {
    // NO API KEY - Using free self-hosted version

    // Basic settings
    placeholder: placeholder,
    height: 400,
    min_height: 200,
    max_height: 500,

    // RTL and language support
    directionality: isRTL ? "rtl" : "ltr",
    language: isRTL ? "fa" : "en",

    // Fix RTL cursor positioning
    text_direction: isRTL ? "rtl" : "ltr",

    // Fix cursor jumping and Enter key issues
    forced_root_block: "p",
    force_p_newlines: true,
    forced_root_block_attrs: { "data-mce-keep": true },
    keep_styles: false,
    remove_trailing_brs: false,

    // Content settings with RTL cursor fix
    content_style: `
      body {
        font-family: ${isRTL ? "Tahoma, Arial, sans-serif" : "Arial, sans-serif"};
        font-size: 14px;
        direction: ${isRTL ? "rtl" : "ltr"};
        text-align: ${isRTL ? "right" : "left"};
        unicode-bidi: ${isRTL ? "bidi-override" : "normal"};
      }
      p {
        margin: 0 0 10px 0;
        direction: ${isRTL ? "rtl" : "ltr"};
        text-align: ${isRTL ? "right" : "left"};
        unicode-bidi: ${isRTL ? "embed" : "normal"};
      }
      /* Fix cursor positioning for RTL */
      ${
        isRTL
          ? `
        * {
          direction: rtl !important;
          text-align: right !important;
        }
        .mce-content-body {
          direction: rtl !important;
          text-align: right !important;
        }
      `
          : ""
      }
    `,

    // Plugins - Free version compatible
    plugins: [
      "advlist",
      "autolink",
      "lists",
      "link",
      "image",
      "charmap",
      "preview",
      "anchor",
      "searchreplace",
      "visualblocks",
      "code",
      "fullscreen",
      "insertdatetime",
      "media",
      "table",
      "help",
      "wordcount",
      "hr",
      "nonbreaking",
      "textpattern",
      "directionality"
    ],

    // Toolbar configuration - Simplified for free version
    toolbar: [
      "undo redo | formatselect | bold italic underline | forecolor backcolor",
      "alignleft aligncenter alignright alignjustify | bullist numlist"
    ].join(" | "),
    //   toolbar: [
    //   "undo redo | formatselect | bold italic underline | forecolor backcolor",
    //   "alignleft aligncenter alignright alignjustify | bullist numlist",
    //   "link image media table | code | fullscreen | ltr rtl"
    // ].join(" | "),

    // Menu bar
    // menubar: "file edit view insert format tools table help",

    // Enter key behavior - Fix jumping cursor
    end_container_on_empty_block: true,
    newline_behavior: "default",

    // Selection and cursor behavior fixes
    inline_boundaries_selector: "a[href],code",
    object_resizing: true,
    table_resize_bars: true,

    // Prevent content jumping
    fix_list_elements: true,
    convert_urls: false,

    // Paste settings
    paste_data_images: true,
    paste_as_text: false,
    paste_retain_style_properties: "all",
    paste_remove_styles_if_webkit: false,

    // Code view settings
    code_dialog_height: 400,
    code_dialog_width: 800,

    // Image settings
    image_advtab: true,
    image_caption: true,
    image_list: [],
    automatic_uploads: true,
    file_picker_types: "image",

    // Table settings
    table_default_attributes: {
      border: "1"
    },
    table_default_styles: {
      "border-collapse": "collapse"
    },

    // Link settings
    link_default_target: "_blank",
    link_assume_external_targets: true,

    // Advanced code sample plugin - Removed (not in free version)
    // codesample_languages: [...],

    // Format options
    block_formats:
      "Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre",

    // Style formats for advanced styling
    style_formats: [
      {
        title: "Headers",
        items: [
          { title: "Header 1", format: "h1" },
          { title: "Header 2", format: "h2" },
          { title: "Header 3", format: "h3" },
          { title: "Header 4", format: "h4" },
          { title: "Header 5", format: "h5" },
          { title: "Header 6", format: "h6" }
        ]
      },
      {
        title: "Inline",
        items: [
          { title: "Bold", icon: "bold", format: "bold" },
          { title: "Italic", icon: "italic", format: "italic" },
          { title: "Underline", icon: "underline", format: "underline" },
          { title: "Strikethrough", icon: "strikethrough", format: "strikethrough" },
          { title: "Superscript", icon: "superscript", format: "superscript" },
          { title: "Subscript", icon: "subscript", format: "subscript" },
          { title: "Code", icon: "code", format: "code" }
        ]
      },
      {
        title: "Blocks",
        items: [
          { title: "Paragraph", format: "p" },
          { title: "Blockquote", format: "blockquote" },
          { title: "Div", format: "div" },
          { title: "Pre", format: "pre" }
        ]
      },
      {
        title: "Alignment",
        items: [
          { title: "Left", icon: "alignleft", format: "alignleft" },
          { title: "Center", icon: "aligncenter", format: "aligncenter" },
          { title: "Right", icon: "alignright", format: "alignright" },
          { title: "Justify", icon: "alignjustify", format: "alignjustify" }
        ]
      }
    ],

    // Emoticons - Removed (not available in free version)
    // emoticons_database_url: '/emoticons/js/emojis.min.js',

    // Auto-focus
    auto_focus: autoFocus,

    // Resize
    resize: "both",

    // Branding
    branding: false,

    // Setup function - Fixed for cursor jumping
    setup: (editor: any) => {
      // Fix cursor jumping issues
      editor.on("init", () => {
        const body = editor.getBody();

        // Prevent cursor jumping on focus
        editor.on("focus", (e: any) => {
          // Don't change cursor position on focus
          e.preventDefault();
        });

        // Fix Enter key behavior
        editor.on("keydown", (e: KeyboardEvent) => {
          if (e.key === "Enter" && !e.shiftKey) {
            // Let TinyMCE handle Enter naturally
            return true;
          }

          // Shift+Enter for line breaks
          if (e.shiftKey && e.key === "Enter") {
            e.preventDefault();
            editor.insertContent("<br>");
            return false;
          }
        });

        // Fix RTL cursor positioning during typing
        editor.on("input", () => {
          if (isRTL) {
            // Fix RTL direction for new content
            const body = editor.getBody();
            const paragraphs = body.querySelectorAll("p");
            paragraphs.forEach((p: any) => {
              if (!p.getAttribute("dir")) {
                p.style.direction = "rtl";
                p.style.textAlign = "right";
                p.setAttribute("dir", "rtl");
              }
            });
          }
        });

        // RTL setup with cursor positioning fix
        if (isRTL) {
          body.style.fontFamily = 'Tahoma, "Iranian Sans", "B Nazanin", Arial, sans-serif';
          body.style.direction = "rtl";
          body.style.textAlign = "right";
          body.style.unicodeBidi = "bidi-override";

          // Fix cursor positioning for RTL text
          body.setAttribute("dir", "rtl");

          // Ensure all paragraphs have RTL direction
          const paragraphs = body.querySelectorAll("p");
          paragraphs.forEach((p: any) => {
            p.style.direction = "rtl";
            p.style.textAlign = "right";
            p.style.unicodeBidi = "embed";
            p.setAttribute("dir", "rtl");
          });
        }
      });

      // Simplified RTL/LTR toggle (no custom CSS button for free version)
      editor.ui.registry.addButton("rtltoggle", {
        text: isRTL ? "EN" : "فا",
        onAction: () => {
          const body = editor.getBody();
          const currentDir = body.dir || body.style.direction;
          const newDir = currentDir === "rtl" ? "ltr" : "rtl";

          body.dir = newDir;
          body.style.direction = newDir;
          body.style.textAlign = newDir === "rtl" ? "right" : "left";

          if (newDir === "rtl") {
            body.style.fontFamily = 'Tahoma, "Iranian Sans", "B Nazanin", Arial, sans-serif';
          } else {
            body.style.fontFamily = "Arial, sans-serif";
          }
        }
      });
    },

    // Event handlers
    init_instance_callback: (editor: any) => {
      if (autoFocus) {
        editor.focus();
      }
    }
  };

  // Custom styles for container
  const containerStyles = {
    direction: isRTL ? ("rtl" as const) : ("ltr" as const),
    textAlign: isRTL ? ("right" as const) : ("left" as const)
  };

  const editorClassName = `tinymce-editor-container ${error ? "tinymce-error" : ""}`;

  console.log("renderrr");

  return (
    <>
      <div className={editorClassName} style={containerStyles}>
        <Editor
          apiKey={apiKey}
          ref={editorRef}
          initialValue={processedInitialValue}
          init={editorConfig}
          onEditorChange={handleEditorChange}
        />
      </div>

      {helperText && (
        <InputHelper error={error} className="mt-1">
          {helperText as string}
        </InputHelper>
      )}
    </>
  );
}
